import React, { useEffect, useState } from 'react';
import MessageBubble from './MessageBubble';
import InputArea from './InputArea';
import ModelStatusBadge from './ModelStatusBadge';
import ThinkingIndicator from './ThinkingIndicator';
import StartupVerification from './StartupVerification';
import { useChatStore } from '../stores/chatStore';
import { invoke } from '@tauri-apps/api/core';
import { modelHealthChecker, ModelHealthStatus } from '../utils/modelHealth';

// Extend window interface for Tauri
declare global {
  interface Window {
    __TAURI__?: any;
  }
}

const ChatInterface: React.FC = () => {
  const [ttsEnabled, setTtsEnabled] = useState(false);
  const { messages, addMessage, setLoading, isLoading } = useChatStore();
  const [modelHealth, setModelHealth] = useState<ModelHealthStatus>({
    isAvailable: false,
    isChecking: false,
    lastChecked: null,
    error: null,
    connectionState: 'disconnected',
    modelName: 'Gemma 3n',
    serviceUrl: 'http://localhost:11434',
    lastSuccessfulCheck: null,
  });
  const [showStartupVerification, setShowStartupVerification] = useState(true);
  const [startupVerificationComplete, setStartupVerificationComplete] = useState(false);

  useEffect(() => {
    const scrollToBottom = () => {
      const chatWindow = document.getElementById('chat-window');
      if (chatWindow) {
        chatWindow.scrollTop = chatWindow.scrollHeight;
      }
    };

    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Subscribe to model health status
    const unsubscribe = modelHealthChecker.subscribe((status) => {
      setModelHealth(status);
    });

    // Start periodic health checks
    modelHealthChecker.startPeriodicCheck(15000); // Check every 15 seconds

    return unsubscribe;
  }, []);

  const handleStartupVerificationComplete = (success: boolean) => {
    setStartupVerificationComplete(success);
    // Hide the verification modal after a short delay
    setTimeout(() => {
      setShowStartupVerification(false);
    }, success ? 1500 : 0); // Show success for 1.5s, hide immediately on failure
  };

  const getErrorMessage = (error: string): string => {
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('connect') || errorLower.includes('connection')) {
      return 'Model not available. Please ensure Gemma 3n is running via Ollama.';
    }
    
    if (errorLower.includes('timeout')) {
      return 'Request timed out. The model is taking too long to respond.';
    }
    
    if (errorLower.includes('empty') || errorLower.includes('malformed')) {
      return 'Empty or malformed response from the model.';
    }
    
    if (errorLower.includes('gemma') || errorLower.includes('ollama')) {
      return 'Gemma 3n model is not running. Please start it via Ollama.';
    }
    
    return `Error: ${error}`;
  };

  const handleSendMessage = async (content: string) => {
    addMessage(content, 'user');
    setLoading(true);
    
    try {
      // Check if running in Tauri environment
      if (typeof window === 'undefined' || !window.__TAURI__) {
        throw new Error('Tauri environment not available. Please run the app in desktop mode.');
      }
      
      // First check if model is available
      const isHealthy = await modelHealthChecker.checkHealth();
      
      if (!isHealthy) {
        throw new Error('Gemma 3n model is not running. Please start it via Ollama.');
      }
      
      console.log('Sending request to generate_llm_response...');
      const response = await invoke<string>('generate_llm_response', { prompt: content });
      
      if (!response || response.trim().length === 0) {
        throw new Error('Empty or malformed response from the model.');
      }
      
      console.log('Received response from LLM:', response.substring(0, 100) + '...');
addMessage(response.trim(), 'assistant');
      if (ttsEnabled) {
        invoke('run_piper_tts', { text: response.trim() }).catch((error) => {
          console.error('TTS Error:', error);
        });
      }
    } catch (error) {
      console.error('Error invoking LLM:', error);
      const errorMessage = getErrorMessage(error instanceof Error ? error.message : String(error));
      addMessage(errorMessage, 'assistant');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header with Status Badge */}
      <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Privacy AI Assistant
          </h1>
          <ModelStatusBadge
            status={modelHealth}
            showDetails={true}
            onRefresh={() => modelHealthChecker.forceCheck()}
          />
        </div>
        
        {/* TTS Toggle */}
        <div className="flex items-center">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={ttsEnabled}
              onChange={() => setTtsEnabled(!ttsEnabled)}
              className="sr-only"
            />
            <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              ttsEnabled ? 'bg-primary-600' : 'bg-gray-300 dark:bg-gray-600'
            }`}>
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                ttsEnabled ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
              🔊 Voice Output
            </span>
          </label>
        </div>
      </div>
      
      {/* Chat window */}
      <div
        id="chat-window"
        className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"
      >
        <div className="max-w-4xl mx-auto px-4 py-6">
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {/* Thinking Indicator */}
          <ThinkingIndicator isVisible={isLoading} />
        </div>
      </div>

      {/* Input area */}
      <InputArea onSendMessage={handleSendMessage} disabled={!modelHealth.isAvailable} />

      {/* Startup Verification Modal */}
      {showStartupVerification && (
        <StartupVerification onVerificationComplete={handleStartupVerificationComplete} />
      )}
    </div>
  );
};

export default ChatInterface;

