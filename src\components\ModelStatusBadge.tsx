import React from 'react';
import { Activity, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/utils/cn';
import { ModelHealthStatus } from '@/utils/modelHealth';

interface ModelStatusBadgeProps {
  status: ModelHealthStatus;
  className?: string;
}

export const ModelStatusBadge: React.FC<ModelStatusBadgeProps> = ({ 
  status, 
  className = '' 
}) => {
  const getStatusConfig = () => {
    if (status.isChecking) {
      return {
        icon: Loader2,
        label: 'Checking...',
        color: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800',
        animate: 'animate-spin'
      };
    }

    if (status.isAvailable) {
      return {
        icon: CheckCircle,
        label: 'Gemma 3n Connected',
        color: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800',
        animate: ''
      };
    }

    return {
      icon: AlertCircle,
      label: 'Model Disconnected',
      color: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-800',
      animate: ''
    };
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className={cn(
      'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border',
      config.color,
      className
    )}>
      <Icon className={cn('w-3 h-3 mr-1.5', config.animate)} />
      {config.label}
      {status.lastChecked && (
        <span className="ml-1 opacity-75">
          • {new Date(status.lastChecked).toLocaleTimeString('en-US', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </span>
      )}
    </div>
  );
};

export default ModelStatusBadge;
