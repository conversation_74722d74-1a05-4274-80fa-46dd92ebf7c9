import React, { useState, useEffect } from 'react';
import { <PERSON>, Mic, MicOff, Volume2, Square } from 'lucide-react';
import { cn } from '@/utils/cn';
import { invoke } from '@tauri-apps/api/core';

// Extend window interface for <PERSON><PERSON>
declare global {
  interface Window {
    __TAURI__?: any;
  }
}

interface VoiceRecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTranscriptionComplete: (text: string) => void;
}

interface SttResult {
  text: string;
  confidence: number;
  success: boolean;
}

export const VoiceRecordingModal: React.FC<VoiceRecordingModalProps> = ({
  isOpen,
  onClose,
  onTranscriptionComplete
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcription, setTranscription] = useState('');
  const [partialTranscription, setPartialTranscription] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);

  // Recording timer
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      setRecordingTime(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  // Auto-close modal when not open
  useEffect(() => {
    if (!isOpen) {
      handleStop();
      setTranscription('');
      setPartialTranscription('');
      setError(null);
      setRecordingTime(0);
    }
  }, [isOpen]);

  const handleStart = async () => {
    try {
      setError(null);
      setIsRecording(true);
      setTranscription('');
      setPartialTranscription('');
      
      // Start recording
      console.log('Starting voice recording...');
      setPartialTranscription('Listening...');
      
      // Simulate real-time transcription updates
      const transcriptionTimer = setInterval(() => {
        if (isRecording) {
          setPartialTranscription(prev => {
            if (prev === 'Listening...') return 'Listening for speech...';
            if (prev === 'Listening for speech...') return 'Processing audio...';
            return prev;
          });
        }
      }, 1000);

      // Auto-stop after 5 seconds (matching backend)
      setTimeout(() => {
        clearInterval(transcriptionTimer);
        handleStop();
      }, 5000);

    } catch (error) {
      console.error('Failed to start recording:', error);
      setError('Failed to start recording. Please check microphone permissions.');
      setIsRecording(false);
    }
  };

  const handleStop = async () => {
    if (!isRecording) return;

    try {
      setIsRecording(false);
      setIsProcessing(true);
      setPartialTranscription('Processing speech...');

      // Check if running in Tauri environment
      if (typeof window === 'undefined' || !window.__TAURI__) {
        throw new Error('Tauri environment not available. Please run the app in desktop mode.');
      }

      // Call the STT backend
      const result = await invoke<SttResult>('run_vosk_stt', { mic_on: true });
      
      if (result.success && result.text) {
        setTranscription(result.text);
        setPartialTranscription('');
        console.log('Transcription completed:', result.text);
      } else {
        throw new Error('Transcription failed or returned empty text');
      }

    } catch (error) {
      console.error('STT Error:', error);
      setError(error instanceof Error ? error.message : 'Failed to process speech');
      setPartialTranscription('');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUse = () => {
    if (transcription.trim()) {
      onTranscriptionComplete(transcription.trim());
      onClose();
    }
  };

  const handleRetry = () => {
    setTranscription('');
    setPartialTranscription('');
    setError(null);
    handleStart();
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md mx-4 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Voice Input
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Recording Status */}
        <div className="text-center mb-6">
          <div className={cn(
            'inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 transition-all duration-300',
            isRecording 
              ? 'bg-red-500 text-white animate-pulse' 
              : isProcessing 
                ? 'bg-blue-500 text-white animate-spin' 
                : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
          )}>
            {isRecording ? (
              <Mic size={32} />
            ) : isProcessing ? (
              <Volume2 size={32} />
            ) : (
              <MicOff size={32} />
            )}
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            {isRecording ? (
              <>
                Recording • {formatTime(recordingTime)}
                <div className="text-xs mt-1">Speak clearly into your microphone</div>
              </>
            ) : isProcessing ? (
              'Processing your speech...'
            ) : (
              'Ready to record'
            )}
          </div>
        </div>

        {/* Live Transcription */}
        <div className="mb-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 min-h-[80px]">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Transcription:
            </div>
            <div className="text-gray-900 dark:text-gray-100">
              {error ? (
                <div className="text-red-500 text-sm">{error}</div>
              ) : transcription ? (
                <div className="font-medium">{transcription}</div>
              ) : partialTranscription ? (
                <div className="text-gray-500 italic">{partialTranscription}</div>
              ) : (
                <div className="text-gray-400 italic">Your speech will appear here...</div>
              )}
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex gap-3">
          {!isRecording && !isProcessing && !transcription && (
            <button
              onClick={handleStart}
              className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              <Mic size={18} />
              Start Recording
            </button>
          )}

          {isRecording && (
            <button
              onClick={handleStop}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              <Square size={18} />
              Stop Recording
            </button>
          )}

          {transcription && !isProcessing && (
            <>
              <button
                onClick={handleRetry}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Retry
              </button>
              <button
                onClick={handleUse}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Use Text
              </button>
            </>
          )}

          {error && (
            <button
              onClick={handleRetry}
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Try Again
            </button>
          )}
        </div>

        {/* Help Text */}
        <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
          Click "Start Recording" and speak clearly. Recording will automatically stop after 5 seconds.
        </div>
      </div>
    </div>
  );
};

export default VoiceRecordingModal;
