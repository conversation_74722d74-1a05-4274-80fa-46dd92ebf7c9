/* Font imports must come BEFORE Tailwind directives */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100%;
  width: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900;
}

/* Loading animation */
.loading-dots {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 20px;
}

.loading-dots div {
  position: absolute;
  top: 8px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: loading-dots 1.2s linear infinite;
}

.loading-dots div:nth-child(1) {
  left: 8px;
  animation-delay: 0s;
}

.loading-dots div:nth-child(2) {
  left: 32px;
  animation-delay: -0.4s;
}

.loading-dots div:nth-child(3) {
  left: 56px;
  animation-delay: -0.8s;
}

@keyframes loading-dots {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

/* Message animation */
.message-enter {
  opacity: 0;
  transform: translateY(20px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.message-exit {
  opacity: 1;
  transform: translateY(0);
}

.message-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Sidebar transition */
.sidebar-enter {
  transform: translateX(-100%);
}

.sidebar-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-out;
}

.sidebar-exit {
  transform: translateX(0);
}

.sidebar-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in;
}

/* Dark mode utilities */
.dark-mode-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Typography */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button variants */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg focus-ring transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg focus-ring transition-colors duration-200;
}

.btn-ghost {
  @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium py-2 px-4 rounded-lg focus-ring transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg focus-ring transition-colors duration-200;
}

/* Input styles */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus-ring transition-colors duration-200;
}

/* Card styles */
.card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
}

/* Markdown content */
.markdown-content {
  @apply text-gray-900 dark:text-gray-100 leading-relaxed;
}

.markdown-content h1 {
  @apply text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100;
}

.markdown-content h2 {
  @apply text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100;
}

.markdown-content h3 {
  @apply text-lg font-medium mb-2 text-gray-900 dark:text-gray-100;
}

.markdown-content p {
  @apply mb-4 text-gray-700 dark:text-gray-300;
}

.markdown-content ul {
  @apply list-disc list-inside mb-4 text-gray-700 dark:text-gray-300;
}

.markdown-content ol {
  @apply list-decimal list-inside mb-4 text-gray-700 dark:text-gray-300;
}

.markdown-content li {
  @apply mb-1;
}

.markdown-content code {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono;
}

.markdown-content pre {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded-lg overflow-x-auto mb-4;
}

.markdown-content pre code {
  @apply bg-transparent p-0;
}

.markdown-content blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-600 dark:text-gray-400 mb-4;
}

.markdown-content a {
  @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 underline;
}

/* Utility classes */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
